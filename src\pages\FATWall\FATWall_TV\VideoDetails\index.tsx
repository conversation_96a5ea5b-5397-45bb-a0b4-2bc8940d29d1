import { FC, useState, useEffect, useMemo, useRef, useCallback } from "react";
import styles from "./index.module.scss";
import { LeftOutlined } from "@ant-design/icons";
import play_white from "@/Resources/player/play_dashboard.png"
import collect_icon_tv from "@/Resources/filmWall/collect_icon_tv.png"
import collect_icon from "@/Resources/filmWall/collect_icon.png"
import watched_icon_tv from "@/Resources/filmWall/watched_icon_tv.png"
import notWatch_icon_tv from "@/Resources/filmWall/notWatch_icon_tv.png"
import douban from "@/Resources/filmWall/douban_icon.png"
import zhixian from "@/Resources/filmWall/zhixian.png"
import { PreloadImage } from "@/components/Image";
import { message } from "antd";
import { Toast } from '@/components/Toast/manager';
import { useHistory, useLocation } from "react-router-dom";
import EpisodeList, { Episode } from "@/components/FATWall_PC/EpisodeList";

import TVFocusable from "../TVFocus";
import { useRequest } from 'ahooks';
import {
  getMediaDetails,
  getMediaFiles,
  collect,
  markWatched,
  MediaDetailsResponse,
  MediaFileInfo,
  collectEpisode,
  markWatchedEpisode,
} from "@/api/fatWall";
import { playVideoTV } from "@/api/fatWallJSBridge";
import CommonUtils from '@/utils/CommonUtils';
import request from '@/request';


interface VideoDetailsProps {
  videoId?: string;
}

// 扩展Episode接口，添加需要的字段
interface ExtendedEpisode extends Episode {
  path?: string;
  file_id?: number;
  resolution?: string;
  hdr?: string;
  audio_codec?: string;
  total_time?: number;
}

// 格式化文件大小的函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};


const VideoDetails: FC<VideoDetailsProps> = ({ videoId }) => {
  const history = useHistory();
  const location = useLocation();

  // 支持URL参数和state参数两种方式
  const urlParams = new URLSearchParams(location.search);
  const urlClasses = urlParams.get('classes');
  const urlMediaId = urlParams.get('media_id');
  const urlLibId = urlParams.get('lib_id');
  const urlIsDrama = urlParams.get('isDrama');

  // 优先使用URL参数，如果没有则使用state参数
  const stateParams = location.state as { classes: string, media_id: number, lib_id?: number, isDrama?: boolean } || { classes: '', media_id: 0 };
  const classes = urlClasses || stateParams.classes || '';
  const media_id = urlMediaId ? parseInt(urlMediaId) : (stateParams.media_id || 0);
  const lib_id = urlLibId ? parseInt(urlLibId) : (stateParams.lib_id || 0); // 默认为0表示"All"页面
  const isDrama = urlIsDrama === 'true' || stateParams.isDrama || (classes === '电视剧');

  const [isLoading, setIsLoading] = useState(true);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [mediaDetails, setMediaDetails] = useState<MediaDetailsResponse | null>(null);
  const [mediaFiles, setMediaFiles] = useState<MediaFileInfo[]>([]);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [isWatched, setIsWatched] = useState<boolean>(false);
  const [currentEpisodeId, setCurrentEpisodeId] = useState<string>("0");
  const [episodeFavorites, setEpisodeFavorites] = useState<Map<number, boolean>>(new Map());
  const [episodeWatched, setEpisodeWatched] = useState<Map<number, boolean>>(new Map());
  const [episodeThumbnails, setEpisodeThumbnails] = useState<Map<number, string>>(new Map()); // 存储每集的缩略图
  const [currentFavoriteOperation, setCurrentFavoriteOperation] = useState<'collect' | 'uncollect' | null>(null);
  const [currentWatchedOperation, setCurrentWatchedOperation] = useState<'watched' | 'unwatched' | null>(null);
  const [selectedVersionId, setSelectedVersionId] = useState<string>('');
  const [availableVersions, setAvailableVersions] = useState<Array<{ id: string, name: string, path: string }>>([]);
  const [versionSelectorOpen, setVersionSelectorOpen] = useState<boolean>(false);
  const [selectedVersionIndex, setSelectedVersionIndex] = useState<number>(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 获取媒体详情
  const { run: runGetMediaDetails } = useRequest(
    getMediaDetails,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaDetails(res.data);
          // 更新收藏和已看状态
          setIsFavorite(res.data.favourite === 1);
          setIsWatched(res.data.seen === 1);
          setIsLoading(false);
        }
      },
      onError: () => {
        message.error('获取影视详情失败');
        setIsLoading(false);
      },
    }
  );

  // 获取媒体文件列表
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0 && res.data) {
          setMediaFiles(res.data.files || []);

          // 初始化单集收藏和观看状态
          const favoriteMap = new Map<number, boolean>();
          const watchedMap = new Map<number, boolean>();

          res.data.files.forEach(file => {
            favoriteMap.set(file.file_id, file.favourite === 1);
            watchedMap.set(file.file_id, file.seen === 1);
          });

          setEpisodeFavorites(favoriteMap);
          setEpisodeWatched(watchedMap);
        }
      },
      onError: () => {
        message.error('获取文件列表失败');
      },
    }
  );

  // 收藏/取消收藏
  const { run: runCollect } = useRequest(
    collect,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isFavorite ? '已添加到收藏' : '已取消收藏');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          setIsFavorite(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          setIsFavorite(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        setIsFavorite(prev => !prev);
      },
    }
  );

  // 标记已观看/未观看
  const { run: runMarkWatched } = useRequest(
    markWatched,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          message.success(isWatched ? '标记为已观看' : '标记为未观看');
        } else if (res.code === 2205) {
          message.error('目标影视项不存在');
          setIsWatched(prev => !prev);
        } else {
          message.error('操作失败，请重试');
          setIsWatched(prev => !prev);
        }
      },
      onError: () => {
        message.error('操作失败，请重试');
        setIsWatched(prev => !prev);
      },
    }
  );

  // 单集收藏/取消收藏
  const { run: runCollectEpisode } = useRequest(
    collectEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          const successMessage = currentFavoriteOperation === 'collect' ? '已添加到收藏' : '已取消收藏';
          message.success(successMessage);
        } else {
          message.error('操作失败，请重试');
          runGetMediaFiles({ lib_id, media_id });
        }
        setCurrentFavoriteOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        runGetMediaFiles({ lib_id, media_id });
        setCurrentFavoriteOperation(null);
      },
    }
  );

  // 单集标记已观看/未观看
  const { run: runMarkWatchedEpisode } = useRequest(
    markWatchedEpisode,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.code === 0) {
          const successMessage = currentWatchedOperation === 'watched' ? '已标记为观看' : '已取消观看';
          message.success(successMessage);
        } else {
          message.error('操作失败，请重试');
          runGetMediaFiles({ lib_id, media_id });
        }
        setCurrentWatchedOperation(null);
      },
      onError: () => {
        message.error('操作失败，请重试');
        runGetMediaFiles({ lib_id, media_id });
        setCurrentWatchedOperation(null);
      },
    }
  );

  // 处理焦点元素的滚动
  const handleFocusScroll = useCallback((item: any) => {
    if (scrollContainerRef.current && item.ref.current) {
      const container = scrollContainerRef.current;
      const element = item.ref.current;
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();

      // 检查元素是否在可视区域外
      if (elementRect.bottom > containerRect.bottom || elementRect.top < containerRect.top) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, []);

  useEffect(() => {
    // 调用接口获取媒体详情
    runGetMediaDetails({ media_id });
    // 调用接口获取媒体文件列表
    runGetMediaFiles({ lib_id, media_id });
  }, [runGetMediaDetails, runGetMediaFiles, media_id, lib_id]);

  // 当mediaFiles更新时，生成版本信息（仅电影类型）
  useEffect(() => {
    if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
      const versions = mediaFiles.map((file) => ({
        id: file.file_id.toString(),
        name: `${file.resolution} | ${file.hdr} | ${file.audio_codec}`,
        path: file.path
      }));
      setAvailableVersions(versions);
      // 设置默认选择第一个版本
      setSelectedVersionId(versions[0].id);
    } else {
      setAvailableVersions([]);
      setSelectedVersionId('');
    }
  }, [mediaFiles, classes]);

  // 获取缩略图 - 支持二进制响应，静默更新
  const getThumbnailWithBinary = (params: { path: string, size: string }) => {
    return request.post('/filemgr/get_thumbnail', params, {
      responseType: 'arraybuffer',
      showLoading: false // 静默更新，不显示loading
    });
  };

  // 获取所有集数的缩略图 - 串行版本，加载完一张设置一张
  const fetchAllThumbnails = useCallback(async () => {
    if (mediaFiles && mediaFiles.length > 0) {
      try {
        // 串行获取每个缩略图，避免并发请求导致进程卡死
        for (const file of mediaFiles) {
          try {
            const res = await getThumbnailWithBinary({
              path: file.path,
              size: "medium"
            });

            if (res instanceof ArrayBuffer) {
              // 检查JPEG文件头
              const uint8Array = new Uint8Array(res);
              const isValidJPEG = uint8Array[0] === 0xFF && uint8Array[1] === 0xD8 && uint8Array[2] === 0xFF;

              let imageUrl: string;
              if (isValidJPEG) {
                const blob = new Blob([res], { type: 'image/jpeg' });
                imageUrl = URL.createObjectURL(blob);
              } else {
                // 尝试作为其他格式
                const blob = new Blob([res], { type: 'image/png' });
                imageUrl = URL.createObjectURL(blob);
              }

              // 加载完一张立即设置一张，提供更好的用户体验
              setEpisodeThumbnails(prev => {
                const newMap = new Map(prev);
                newMap.set(file.file_id, imageUrl);
                return newMap;
              });
            }
          } catch (error) {
            console.error(`获取第${file.file_id}集缩略图失败:`, error);
            // 单个失败不影响其他缩略图的获取，继续处理下一个
          }
        }

      } catch (error) {
        console.error('批量获取缩略图失败:', error);
      }
    }
  }, [mediaFiles]);

  // 当mediaFiles更新后延迟获取缩略图
  useEffect(() => {
    if (mediaFiles && mediaFiles.length > 0 && classes !== '电影') {
      // 延迟500毫秒开始获取缩略图，等待组件渲染完成
      const timer = setTimeout(() => {
        fetchAllThumbnails();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [mediaFiles, fetchAllThumbnails, classes]);

  // 清理blob URLs
  useEffect(() => {
    return () => {
      // 组件卸载时清理所有blob URLs
      episodeThumbnails.forEach(url => {
        URL.revokeObjectURL(url);
      });
    };
  }, [episodeThumbnails]);

  const handleBack = () => {
    history.goBack();
  };

  const handlePlay = () => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建完整的videoList数组，与APP端保持一致
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: (file.media_id || media_id).toString(), // 如果file.media_id为空，使用页面的media_id
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    let playIndex = 0; // 默认播放第一集

    // 根据classes判断播放逻辑
    if (classes === '电影') {
      // 电影：根据版本选择器确定播放索引
      if (mediaFiles.length >= 2 && selectedVersionId) {
        const selectedFileIndex = mediaFiles.findIndex(file => file.file_id.toString() === selectedVersionId);
        if (selectedFileIndex !== -1) {
          playIndex = selectedFileIndex;
        }
      }
    } else {
      // 剧集类型：根据media_details中的last_seen_file_id在file_list数组中筛选file_id相同的项的索引位置
      if (mediaDetails?.last_seen_file_id) {
        // 遍历mediaFiles数组，找到file_id与last_seen_file_id相同的项的索引
        const targetIndex = mediaFiles.findIndex(file => file.file_id === mediaDetails.last_seen_file_id);
        if (targetIndex !== -1) {
          playIndex = targetIndex;
          console.log(`TV端剧集播放：找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的索引位置：${targetIndex}`);
        } else {
          console.log(`TV端剧集播放：未找到last_seen_file_id(${mediaDetails.last_seen_file_id})对应的文件，将播放第一集`);
        }
      } else {
        console.log('TV端剧集播放：没有last_seen_file_id，将播放第一集');
      }
    }

    // 调用视频播放接口
    playVideoTV(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show('开始播放');
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });
  };



  const handleToggleFavorite = () => {
    const newFavoriteStatus = !isFavorite;
    setIsFavorite(newFavoriteStatus);

    // 调用收藏API
    runCollect({
      media_ids: [media_id],
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleWatched = () => {
    const newWatchedStatus = !isWatched;
    setIsWatched(newWatchedStatus);

    // 调用标记已观看API
    runMarkWatched({
      media_ids: [media_id],
      seen: newWatchedStatus ? 1 : 0
    });
  };

  const toggleDescription = useCallback(() => {
    setShowFullDescription(prev => !prev);
  }, []);

  // 处理版本选择
  const handleVersionSelect = useCallback((versionId: string) => {
    setSelectedVersionId(versionId);
    const selectedVersion = availableVersions.find(v => v.id === versionId);
    if (selectedVersion) {
      Toast.show(`已切换到版本: ${selectedVersion.name}`);
    }
  }, [availableVersions]);

  // 处理版本选择器点击/回车事件
  const handleVersionSelectorClick = () => {
    // 切换弹窗的展开状态
    setVersionSelectorOpen(prev => !prev);
    if (!versionSelectorOpen) {
      // 打开弹窗时重置选中索引为当前版本
      const currentIndex = availableVersions.findIndex(v => v.id === selectedVersionId);
      setSelectedVersionIndex(currentIndex >= 0 ? currentIndex : 0);
    }
  };

  // 处理弹窗内的键盘导航
  const handleVersionModalKeyDown = useCallback((e: KeyboardEvent) => {
    if (!versionSelectorOpen) return;

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        setSelectedVersionIndex(prev =>
          prev > 0 ? prev - 1 : availableVersions.length - 1
        );
        break;
      case 'ArrowDown':
        e.preventDefault();
        setSelectedVersionIndex(prev =>
          prev < availableVersions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'Enter':
        e.preventDefault();
        const selectedVersion = availableVersions[selectedVersionIndex];
        if (selectedVersion) {
          handleVersionSelect(selectedVersion.id);
          setVersionSelectorOpen(false);
        }
        break;
      case 'Escape':
      case 'Backspace':
        e.preventDefault();
        setVersionSelectorOpen(false);
        break;
    }
  }, [versionSelectorOpen, selectedVersionIndex, availableVersions, handleVersionSelect]);

  // 监听键盘事件
  useEffect(() => {
    if (versionSelectorOpen) {
      window.addEventListener('keydown', handleVersionModalKeyDown);
      return () => {
        window.removeEventListener('keydown', handleVersionModalKeyDown);
      };
    }
  }, [versionSelectorOpen, handleVersionModalKeyDown]);

  // 获取当前选择的版本对应的文件信息
  const selectedVersionFile = useMemo(() => {
    if (classes === '电影' && mediaFiles && mediaFiles.length >= 2) {
      // 电影类型：根据selectedVersionId找到对应的文件
      const selectedFile = mediaFiles.find(file => file.file_id.toString() === selectedVersionId);
      return selectedFile || mediaFiles[0]; // 如果没找到，返回第一个文件
    } else {
      // 非电影类型：返回第一个文件
      return mediaFiles && mediaFiles.length > 0 ? mediaFiles[0] : null;
    }
  }, [classes, mediaFiles, selectedVersionId]);

  // 计算播放按钮显示文本
  const getPlayButtonText = useMemo(() => {
    // 如果是电视剧类型，显示上次播放的集数
    if (classes === '电视剧') {
      const lastSeenFileId = mediaDetails?.last_seen_file_id;
      if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
        const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
        if (lastSeenFile && lastSeenFile.episode) {
          return `播放 第${lastSeenFile.episode}集`;
        }
      }
      // 没有播放记录时显示第1集
      return mediaFiles && mediaFiles.length > 0 ? '播放 第1集' : '播放';
    } else {
      // 根据 mediaDetails 中的 last_seen_file_id 去 mediaFiles 中找到匹配的文件
      const lastSeenFileId = mediaDetails?.last_seen_file_id;
      if (lastSeenFileId && mediaFiles && mediaFiles.length > 0) {
        const lastSeenFile = mediaFiles.find(file => file.file_id === lastSeenFileId);
        if (lastSeenFile) {
          const lastPlayPoint = lastSeenFile.last_play_point || 0;
          if (lastPlayPoint > 0) {
            const minutes = Math.floor(lastPlayPoint / 60);
            const seconds = lastPlayPoint % 60;
            return `播放 ${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
          }
        }
      }
      return '播放';
    }
  }, [classes, mediaDetails, mediaFiles]);



  const handleToggleEpisodeFavorite = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newFavoriteStatus = !episode.favorite;
    const operationType = newFavoriteStatus ? 'collect' : 'uncollect';

    // 保存当前操作类型
    setCurrentFavoriteOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeFavorites(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newFavoriteStatus);
      return newMap;
    });

    // 调用API
    runCollectEpisode({
      file_id: episode.file_id,
      favourite: newFavoriteStatus ? 1 : 0
    });
  };

  const handleToggleEpisodeWatched = (episode: Episode) => {
    if (!episode.file_id) {
      message.error('无法获取剧集文件ID');
      return;
    }

    const newWatchedStatus = !episode.watched;
    const operationType = newWatchedStatus ? 'watched' : 'unwatched';

    // 保存当前操作类型
    setCurrentWatchedOperation(operationType);

    // 先更新本地状态，提供即时反馈
    setEpisodeWatched(prev => {
      const newMap = new Map(prev);
      newMap.set(episode.file_id!, newWatchedStatus);
      return newMap;
    });

    // 调用API
    runMarkWatchedEpisode({
      file_id: episode.file_id,
      seen: newWatchedStatus ? 1 : 0
    });
  };

  const handleEpisodeSelect = (episode: Episode) => {
    setCurrentEpisodeId(episode.id);

    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建完整的videoList数组
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: (file.media_id || media_id).toString(), // 如果file.media_id为空，使用页面的media_id
      file_id: file.file_id.toString(),
      total_time: file.duration || 0, // 视频时长
      filter: {
        last_play_point: file.last_play_point,
        audio_index: file.audio_index,
        seen: file.seen,
        subtitle_path: file.subtitle_path,
        subtitle_type: file.subtitle_type,
        subtitle_index: file.subtitle_index,
      }
    }));

    // 根据选中的剧集找到对应的索引
    const selectedEpisodeIndex = mediaFiles.findIndex(file => file.file_id.toString() === episode.id);
    const playIndex = selectedEpisodeIndex !== -1 ? selectedEpisodeIndex : 0;

    // 调用视频播放接口
    playVideoTV(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show(`开始播放第${episode.episodeNumber}集`);
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });
  };

  // 将媒体文件转换为剧集列表格式
  const episodes: ExtendedEpisode[] = useMemo(() => {
    if (!mediaFiles || mediaFiles.length === 0) {
      return [];
    }

    return mediaFiles.map((file) => {
      // 获取该集的缩略图，如果没有则使用默认poster
      const thumbnailUrl = episodeThumbnails.get(file.file_id);
      const posterUrl = thumbnailUrl || (mediaDetails?.poster && mediaDetails.poster.length > 0 ? mediaDetails.poster[0] : '');

      return {
        id: file.file_id.toString(),
        title: `第${file.episode}集`,
        thumbnail: posterUrl,
        episodeNumber: file.episode,
        watched: episodeWatched.get(file.file_id) ?? false,
        progress: file.last_play_point > 0 ? Math.min(Math.round(file.last_play_point), 100) : 0,
        favorite: episodeFavorites.get(file.file_id) ?? false,
        file_id: file.file_id,
        path: file.path,
        resolution: file.resolution,
        hdr: file.hdr,
        audio_codec: file.audio_codec,
        file_media_id: file?.media_id
      };
    });
  }, [mediaFiles, mediaDetails, episodeFavorites, episodeWatched, episodeThumbnails]);

  // 创建演员列表数据
  const castMembers = useMemo(() => {
    if (!mediaDetails || !mediaDetails.actor_info) return [];

    return mediaDetails.actor_info.map((actor, index) => ({
      id: index.toString(),
      name: actor?.name || '未知演员',
      role: actor.chapter ? `饰 ${actor.chapter}` : '演员',
      avatar: actor.profile_path || ''
    }));
  }, [mediaDetails]);

  // 视频信息，使用真实数据
  const videoData = useMemo(() => {
    if (!mediaDetails) {
      return {
        title: '加载中...',
        rating: 0,
        year: 0,
        category: '加载中',
        region: '',
        duration: '0分钟',
        tags: [],
        description: '加载中...',
        filePath: '',
        fileSize: '0 B',
        cast: []
      };
    }

    // 使用接口返回的真实数据
    return {
      title: mediaDetails.trans_name || mediaDetails.origin_name || '未知影片',
      rating: mediaDetails.score || 0,
      year: mediaDetails.year || 0,
      category: mediaDetails.classes || '未知',
      region: mediaDetails.origin_place || '未知',
      duration: (() => {
        const totalMinutes = mediaDetails.video_length;
        if (!totalMinutes) return '未知时长';

        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;

        if (hours === 0) {
          return `${minutes}分钟`;
        } else if (minutes === 0) {
          return `${hours}小时`;
        } else {
          return `${hours}小时${minutes}分钟`;
        }
      })(),
      tags: selectedVersionFile ? [
        selectedVersionFile.resolution,
        selectedVersionFile.hdr,
        selectedVersionFile.audio_codec
      ].filter(Boolean) : [],
      description: mediaDetails.brief || '暂无简介',
      filePath: selectedVersionFile ? selectedVersionFile.path : '',
      fileSize: selectedVersionFile ? formatFileSize(selectedVersionFile.file_size || 0) : '未知大小',
      cast: castMembers
    };
  }, [mediaDetails, castMembers, selectedVersionFile]);

  // 计算动态行坐标
  const getRowCoordinates = useMemo(() => {
    const hasVersionSelector = classes === '电影' && availableVersions.length >= 2;
    const isDrama = classes === '电视剧';

    return {
      description: 0,           // 第1行：视频描述
      versionSelector: 1,       // 第2行：版本选择器（仅电影且有多版本时）
      buttons: hasVersionSelector ? 2 : 1,  // 第3行或第2行：操作按钮
      episodeList: hasVersionSelector ? 3 : 2,  // 第4行或第3行：剧集列表
      cast: isDrama ?
        (hasVersionSelector ? 4 : 3) :     // 电视剧：第4行或第3行（剧集列表之后）
        (hasVersionSelector ? 3 : 2),      // 电影/其他：第4行或第3行（与剧集列表同行，因为没有剧集列表）
    };
  }, [classes, availableVersions.length]);

  // 渲染视频描述
  const renderDescription = useMemo(() => {
    const description = videoData.description || '';
    const isLongText = description.length > 73;

    if (!isLongText) {
      // 如果文本不够长，直接显示全部内容，不显示展开/收起按钮
      return (
        <div className={styles.fullDescription}>
          {description}
        </div>
      );
    }

    return showFullDescription ? (
      <div className={styles.fullDescription}>
        {description}
        <TVFocusable
          id="tv-focus-videoDetails-description-collapse"
          row={getRowCoordinates.description}
          col={0}
          onClick={toggleDescription}
          className={styles.toggleButton}
          currentItem={handleFocusScroll}
        >
          收起
        </TVFocusable>
      </div>
    ) : (
      <div className={styles.limitedDescription}>
        <span>{description.substring(0, 85)}...</span>
        <TVFocusable
          id="tv-focus-videoDetails-description-expand"
          row={getRowCoordinates.description}
          col={0}
          onClick={toggleDescription}
          className={styles.toggleButton}
          currentItem={handleFocusScroll}
        >
          更多
        </TVFocusable>
      </div>
    );
  }, [showFullDescription, videoData.description, toggleDescription, handleFocusScroll, getRowCoordinates.description]);

  // 加载中状态
  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>加载中...</div>
        {/* 返回按钮 - 固定在顶部 */}
        <TVFocusable
          id="tv-focus-videoDetails-back-button"
          row={0}
          col={0}
          onClick={handleBack}
          className={styles.backButton}
          currentItem={handleFocusScroll}
        >
          <LeftOutlined />
        </TVFocusable>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* 背景视频/图片 */}
      <div className={styles.videoBackground}>
        {mediaDetails?.poster && mediaDetails.poster.length > 0 && (
          <img src={mediaDetails.poster[1]||mediaDetails.poster[0]} alt={videoData.title} />
        )}
      </div>


      {/* 可滚动的内容区域 */}
      <div className={`${styles.scrollableContent} ${isDrama ? styles.dramaScrollableContent : ''}`} ref={scrollContainerRef}>
        {/* 视频标题 */}
        <h1 className={styles.title}>{videoData.title}</h1>

        {/* 视频信息行 */}
        {classes !== '其他' && (
          <div className={styles.infoRow}>
            <div className={styles.rating}>
              <PreloadImage src={douban} style={{ width: "20px", height: "20px" }} alt="play_white" />
              <span >{Number(videoData.rating).toFixed(1)}</span>
            </div>
            <PreloadImage src={zhixian} style={{ width: "2px", height: "30px" }} alt="play_white" />
            <span>{videoData.year}</span>
            <PreloadImage src={zhixian} style={{ width: "2px", height: "30px" }} alt="play_white" />
            <span>{videoData.category}</span>
            <PreloadImage src={zhixian} style={{ width: "2px", height: "30px" }} alt="play_white" />
            <span>{videoData.region}</span>
            <PreloadImage src={zhixian} style={{ width: "2px", height: "30px" }} alt="play_white" />
            <span>{videoData.duration}</span>
            <PreloadImage src={zhixian} style={{ width: "2px", height: "30px" }} alt="play_white" />
            {/* 视频标签 */}
            {classes === '电影' && (
              <div className={styles.infoRow}>
                {videoData.tags.map((tag, index) => (
                  <span key={index} className={styles.tag}>{tag}</span>
                ))}
              </div>
            )}

          </div>
        )}

        {/* 视频描述 */}
        {classes !== '其他' && (
          <div className={styles.descriptionContainer}>
            {renderDescription}
          </div>
        )}

        {/* 版本选择器 - 只在电影类型且有多个版本时显示 */}
        {classes === '电影' && availableVersions.length >= 2 && (
          <div className={styles.versionSelector}>
            <TVFocusable
              id="tv-focus-videoDetails-version-selector"
              row={getRowCoordinates.versionSelector}
              col={0}
              onClick={handleVersionSelectorClick}
              className={styles.versionSelectWrapper}
              currentItem={handleFocusScroll}
            >
              <div className={styles.customVersionSelect}>
                <span className={styles.versionLabel}>选择版本：</span>
                <div className={styles.versionDisplay}>
                  {availableVersions.find(v => v.id === selectedVersionId)?.name || '请选择版本'}
                  <span className={styles.dropdownArrow}>▼</span>
                </div>
              </div>
            </TVFocusable>
          </div>
        )}

        {/* 版本选择弹窗 */}
        {versionSelectorOpen && (
          <div className={styles.versionModal}>
            <div className={styles.versionModalContent}>
              <div className={styles.versionModalHeader}>
                <h3>选择版本</h3>
              </div>
              <div className={styles.versionModalBody}>
                {availableVersions.map((version, index) => (
                  <div
                    key={version.id}
                    className={`${styles.versionOption} ${
                      index === selectedVersionIndex ? styles.versionOptionSelected : ''
                    }`}
                  >
                    {version.name}
                  </div>
                ))}
              </div>
              <div className={styles.versionModalFooter}>
                <div className={styles.versionModalHint}>
                  ↑↓ 选择 • Enter 确认 • Esc 取消
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className={styles.buttons}>
          <TVFocusable
            id="tv-focus-videoDetails-play-button"
            row={getRowCoordinates.buttons}
            col={0}
            onClick={handlePlay}
            className={styles.primaryButton}
            currentItem={handleFocusScroll}
          >
            <PreloadImage src={play_white} style={{ width: "60px", height: "60px" }} alt="play_white" />
            <div className={styles.text}>{getPlayButtonText}</div>
          </TVFocusable>

          <TVFocusable
            id="tv-focus-videoDetails-favorite-button"
            row={getRowCoordinates.buttons}
            col={1}
            onClick={handleToggleFavorite}
            className={styles.secondaryButton}
            currentItem={handleFocusScroll}
          >
            {isFavorite ?
              <PreloadImage src={collect_icon} style={{ width: "44px", height: "44px" }} alt="collect_icon_tv" /> :
              <PreloadImage src={collect_icon_tv} style={{ width: "44px", height: "44px" }} alt="collect_icon_tv" />
            }
            <div className={styles.text}>收藏</div>
          </TVFocusable>

          <TVFocusable
            id="tv-focus-videoDetails-watched-button"
            row={getRowCoordinates.buttons}
            col={2}
            onClick={handleToggleWatched}
            className={styles.secondaryButton}
            currentItem={handleFocusScroll}
          >
            {
              isWatched ?
                <PreloadImage src={notWatch_icon_tv} style={{ width: "44px", height: "44px" }} alt="watched_icon_tv" /> :
                <PreloadImage src={watched_icon_tv} style={{ width: "44px", height: "44px" }} alt="notWatch_icon_tv" />
            }
            <div className={styles.text}>已观看</div>
          </TVFocusable>
        </div>


        {/* 剧集列表 */}
        <div className={styles.episodeList}>
          {classes === '电视剧' && mediaFiles.length > 0 && (
            <EpisodeList
              episodes={episodes}
              isTv={true}
              currentEpisodeId={currentEpisodeId}
              onEpisodeSelect={handleEpisodeSelect}
              onToggleFavorite={handleToggleEpisodeFavorite}
              onToggleWatched={handleToggleEpisodeWatched}
              baseRow={getRowCoordinates.episodeList}
            />
          )}
        </div>



        {/* 需要向上滚动才能看到的内容 */}
        <div className={isDrama ? styles.extraInfoSection : ''}>
          {/* 演员列表 */}
          {classes !== '其他' && (

            <div className={styles.castSection}>
              <h3 className={styles.sectionTitle}>演员人员</h3>
              <div className={styles.castList}>
                {videoData.cast.map((member) => (
                  <TVFocusable
                    key={member.id}
                    id={`tv-focus-videoDetails-cast-${member.id}`}
                    row={getRowCoordinates.cast}
                    col={parseInt(member.id)}
                    onClick={() => { }}
                    className={styles.castItem}
                    currentItem={handleFocusScroll}
                  >
                    {member.avatar ? (
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className={styles.castAvatar}
                      />
                    ) : (
                      <div className={styles.castAvatar}>{member.name.charAt(0)}</div>
                    )}
                    <div className={styles.castName}>{member.name}</div>
                    <div className={styles.castRole}>{member.role}</div>
                  </TVFocusable>
                ))}
              </div>
            </div>)}


          {/* 文件信息 */}
          {classes !== '电视剧' && (
            <div className={styles.fileInfo}>
              <div className={styles.fileInfoItem}>文件路径：{CommonUtils.formatFilePath(videoData.filePath)}</div>
              <div className={styles.fileInfoItem}>文件大小：{videoData.fileSize}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoDetails;